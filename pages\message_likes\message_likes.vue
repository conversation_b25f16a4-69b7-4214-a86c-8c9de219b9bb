<template>
	<view class="likes-container">
		<!-- 背景装饰元素 -->
		<view class="gradient-blob blob-1"></view>
		<view class="gradient-blob blob-2"></view>
		<view class="gradient-blob blob-3"></view>
		<view class="decorative-pattern"></view>

		<!-- 页面头部 -->
		<!-- <view class="page-header">
			<text class="page-title">那些被看见的温柔~</text>
		</view> -->

		<!-- 分类标签栏 -->
		<view class="tab-container">
			<view class="tab-bar">
				<!-- 滑块指示器 -->
				<view class="tab-slider" :class="{ 'slider-right': activeTab === 'given' }"></view>

				<view
					class="tab-item"
					:class="{ active: activeTab === 'received' }"
					@tap="switchTab('received')"
					hover-class="none"
				>
					<view class="tab-icon-wrapper">
						<view class="tab-icon received-icon"></view>
					</view>
					<text class="tab-text">被点赞</text>
					<view class="tab-count" v-if="receivedCount > 0">{{ receivedCount }}</view>
				</view>

				<view
					class="tab-item"
					:class="{ active: activeTab === 'given' }"
					@tap="switchTab('given')"
					hover-class="none"
				>
					<view class="tab-icon-wrapper">
						<view class="tab-icon given-icon"></view>
					</view>
					<text class="tab-text">我点赞</text>
					<view class="tab-count" v-if="givenCount > 0">{{ givenCount }}</view>
				</view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content-wrapper">
			<!-- 被点赞列表 -->
			<view v-if="activeTab === 'received'" class="likes-section">
				<view v-if="receivedLikes.length === 0 && !isLoading" class="empty-state">
					<view class="empty-illustration">
						<view class="empty-heart-icon"></view>
						<view class="empty-sparkles">
							<view class="sparkle sparkle-1"></view>
							<view class="sparkle sparkle-2"></view>
							<view class="sparkle sparkle-3"></view>
						</view>
					</view>
					<text class="empty-title">还没有人给你点赞</text>
					<text class="empty-subtitle">写更多精彩的留言来获得点赞吧~</text>
				</view>

				<view v-else class="likes-list">
					<view
						v-for="(item, index) in receivedLikes"
						:key="index"
						class="like-card"
						@tap="viewMessage(item.message_id)"
						hover-class="none"
					>
						<view class="card-header">
							<view class="user-info">
								<view class="avatar-wrapper">
									<image :src="formatAvatarUrl(item.liker_avatar)" mode="aspectFill" class="user-avatar"></image>
								</view>
								<view class="user-details">
									<text class="user-name">{{ item.liker_name || '匿名用户' }}</text>
									<text class="action-text">给你点了个赞</text>
								</view>
							</view>
							<view class="time-info">
								<text class="like-time">{{ formatTime(item.created_at) }}</text>
								<view class="heart-indicator">
									<view class="heart-icon"></view>
								</view>
							</view>
						</view>
						<view class="message-preview">
							<text class="message-text">{{ item.message_content }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 我点赞列表 -->
			<view v-if="activeTab === 'given'" class="likes-section">
				<view v-if="givenLikes.length === 0 && !isLoading" class="empty-state">
					<view class="empty-illustration">
						<view class="empty-heart-icon"></view>
						<view class="empty-sparkles">
							<view class="sparkle sparkle-1"></view>
							<view class="sparkle sparkle-2"></view>
							<view class="sparkle sparkle-3"></view>
						</view>
					</view>
					<text class="empty-title">你还没有点过赞</text>
					<text class="empty-subtitle">去发现更多精彩的留言吧~</text>
				</view>

				<view v-else class="likes-list">
					<view
						v-for="(item, index) in givenLikes"
						:key="index"
						class="like-card"
						@tap="viewMessage(item.message_id)"
						hover-class="none"
					>
						<view class="card-header">
							<view class="user-info">
								<view class="avatar-wrapper">
									<image :src="formatAvatarUrl(item.author_avatar)" mode="aspectFill" class="user-avatar"></image>
								</view>
								<view class="user-details">
									<text class="user-name">{{ item.author_name || '匿名作者' }}</text>
									<text class="action-text">你点赞了TA的留言</text>
								</view>
							</view>
							<view class="time-info">
								<text class="like-time">{{ formatTime(item.created_at) }}</text>
								<view class="heart-indicator">
									<view class="heart-icon"></view>
								</view>
							</view>
						</view>
						<view class="message-preview">
							<text class="message-text">{{ item.message_content }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="isLoading" class="loading-overlay">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				activeTab: 'received', // 'received' 或 'given'
				receivedLikes: [], // 被点赞列表
				givenLikes: [], // 我点赞列表
				receivedCount: 0,
				givenCount: 0,
				isLoading: false,
				token: null
			}
		},
		onLoad() {
			this.token = uni.getStorageSync('mailbox_token');
			this.loadLikesData();
		},
		methods: {
			// 切换标签
			switchTab(tab) {
				this.activeTab = tab;
				this.loadLikesData();
			},

			// 加载点赞数据
			async loadLikesData() {
				if (!this.token) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					return;
				}

				this.isLoading = true;

				try {
					const endpoint = this.activeTab === 'received' ? 'received_likes' : 'given_likes';
					const res = await uni.request({
						url: `${this.$baseUrl}/mailbox/likes/${endpoint}`,
						method: 'GET',
						header: {
							'Authorization': `Bearer ${this.token}`
						}
					});

					if (res.statusCode === 200 && res.data) {
						if (this.activeTab === 'received') {
							this.receivedLikes = res.data.likes || [];
							this.receivedCount = res.data.total || 0;
						} else {
							this.givenLikes = res.data.likes || [];
							this.givenCount = res.data.total || 0;
						}
					} else if (res.statusCode === 401) {
						uni.showToast({
							title: '登录已过期，请重新登录',
							icon: 'none'
						});
					} else {
						console.error('获取点赞数据失败:', res);
					}
				} catch (error) {
					console.error('请求点赞数据失败:', error);
					uni.showToast({
						title: '网络请求失败',
						icon: 'none'
					});
				} finally {
					this.isLoading = false;
				}
			},

			// 查看留言详情
			viewMessage(messageId) {
				if (!messageId) return;

				// 找到对应的留言数据
				let messageData = null;
				if (this.activeTab === 'received') {
					messageData = this.receivedLikes.find(item => item.message_id === messageId);
				} else {
					messageData = this.givenLikes.find(item => item.message_id === messageId);
				}

				if (!messageData || !messageData.message_name) {
					uni.showToast({
						title: '无法获取留言信息',
						icon: 'none'
					});
					return;
				}

				// 跳转到查看留言页面，传递收信人姓名
				uni.navigateTo({
					url: `/pages/view/view?name=${encodeURIComponent(messageData.message_name.trim())}`
				});
			},

			// 格式化头像URL
			formatAvatarUrl(url) {
				if (!url) {
					return '/static/default-avatar.png';
				}
				if (url.startsWith('http')) {
					return url;
				}
				const baseUrl = this.$baseUrl.endsWith('/') ? this.$baseUrl.slice(0, -1) : this.$baseUrl;
				const avatarPath = url.startsWith('/') ? url : `/${url}`;
				return baseUrl + avatarPath;
			},

			// 格式化时间
			formatTime(timestamp) {
				if (!timestamp) return '';

				const now = new Date();
				const time = new Date(timestamp);
				const diff = now - time;

				const minute = 60 * 1000;
				const hour = 60 * minute;
				const day = 24 * hour;

				if (diff < minute) {
					return '刚刚';
				} else if (diff < hour) {
					return `${Math.floor(diff / minute)}分钟前`;
				} else if (diff < day) {
					return `${Math.floor(diff / hour)}小时前`;
				} else if (diff < 7 * day) {
					return `${Math.floor(diff / day)}天前`;
				} else {
					return time.toLocaleDateString();
				}
			}
		}
	}
</script>

<style lang="scss">
/* 基础容器样式 */
.likes-container {
	min-height: 100vh;
	background: linear-gradient(180deg, #fdfbfb 0%, #f6f7fb 100%);
	background-attachment: fixed;
	position: relative;
	overflow-x: hidden;
	overflow-y: auto;
	padding-top: 30rpx;
	box-sizing: border-box;
	-webkit-tap-highlight-color: transparent;
	tap-highlight-color: transparent;
}

/* 移除点击高亮 */
.likes-container view {
	-webkit-tap-highlight-color: transparent;
	tap-highlight-color: transparent;
}

/* 背景装饰元素 - 与其他页面保持一致 */
.gradient-blob {
	position: fixed;
	border-radius: 50%;
	filter: blur(60rpx);
	opacity: 0.6;
	z-index: 0;
	animation: float 20s ease-in-out infinite;
}

.blob-1 {
	width: 300rpx;
	height: 300rpx;
	background: linear-gradient(135deg, rgba(255, 107, 129, 0.4), rgba(255, 158, 158, 0.3));
	top: 10%;
	left: -10%;
	animation-delay: 0s;
}

.blob-2 {
	width: 250rpx;
	height: 250rpx;
	background: linear-gradient(135deg, rgba(165, 199, 254, 0.4), rgba(132, 250, 176, 0.3));
	top: 60%;
	right: -10%;
	animation-delay: -7s;
}

.blob-3 {
	width: 200rpx;
	height: 200rpx;
	background: linear-gradient(135deg, rgba(254, 225, 64, 0.4), rgba(255, 169, 198, 0.3));
	bottom: 20%;
	left: 20%;
	animation-delay: -14s;
}

@keyframes float {
	0%, 100% {
		transform: translateY(0px) rotate(0deg);
	}
	33% {
		transform: translateY(-30rpx) rotate(120deg);
	}
	66% {
		transform: translateY(20rpx) rotate(240deg);
	}
}

.decorative-pattern {
	position: fixed;
	width: 100%;
	height: 100vh;
	top: 0;
	left: 0;
	background-image: radial-gradient(rgba(0, 0, 0, 0.08) 0.5px, transparent 0.5px);
	background-size: 15rpx 15rpx;
	opacity: 0.6;
	z-index: 0;
	animation: patternPulse 15s ease-in-out infinite;
}

@keyframes patternPulse {
	0%, 100% { opacity: 0.4; }
	50% { opacity: 0.25; }
}

/* 页面头部 */
.page-header {
	text-align: center;
	padding: 0rpx 30rpx 20rpx;
	position: relative;
	z-index: 1;
}

.page-title {
	font-size: 48rpx;
	font-weight: 700;
	color: #333;
	display: block;
	background: linear-gradient(135deg, #ff6b81, #a5c7fe);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

/* 标签栏容器 */
.tab-container {
	padding: 0 30rpx 20rpx;
	position: relative;
	z-index: 1;
}

.tab-bar {
	display: flex;
	background: rgba(255, 255, 255, 0.9);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-radius: 25rpx;
	padding: 8rpx;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
	position: relative;
}

/* 滑块指示器 */
.tab-slider {
	position: absolute;
	top: 8rpx;
	left: 8rpx;
	width: calc(50% - 8rpx);
	height: calc(100% - 16rpx);
	background: linear-gradient(135deg, rgba(255, 107, 129, 0.2), rgba(255, 158, 158, 0.15));
	border-radius: 20rpx;
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	z-index: 1;
	box-shadow: 0 2rpx 8rpx rgba(255, 107, 129, 0.15);
}

.tab-slider.slider-right {
	left: calc(50% + 0rpx);
}

.tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 15rpx;
	border-radius: 20rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	cursor: pointer;
	-webkit-tap-highlight-color: transparent;
	tap-highlight-color: transparent;
	z-index: 2;
}

.tab-item.active {
	transform: translateY(-1rpx);
}

.tab-icon-wrapper {
	position: relative;
	margin-bottom: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.tab-icon {
	width: 36rpx;
	height: 36rpx;
	position: relative;
}

.received-icon::before {
	content: '';
	position: absolute;
	width: 36rpx;
	height: 36rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff6b81' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
	background-size: cover;
	background-repeat: no-repeat;
	transition: all 0.3s ease;
}

.given-icon::before {
	content: '';
	position: absolute;
	width: 36rpx;
	height: 36rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23a5c7fe' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
	background-size: cover;
	background-repeat: no-repeat;
	transition: all 0.3s ease;
}

.tab-item.active .received-icon::before {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23ff6b81' stroke='%23ff6b81' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
	transform: scale(1.1);
}

.tab-item.active .given-icon::before {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23a5c7fe' stroke='%23a5c7fe' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
	transform: scale(1.1);
}

.tab-text {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
	transition: all 0.3s ease;
	margin-bottom: 4rpx;
}

.tab-item.active .tab-text {
	color: #333;
	font-weight: 600;
}

.tab-count {
	position: absolute;
	top: -10rpx;
	right: -18rpx;
	background: linear-gradient(135deg, #ff6b81, #ff9a9e);
	color: #fff;
	font-size: 22rpx;
	font-weight: 600;
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	min-width: 28rpx;
	text-align: center;
	line-height: 1;
	box-shadow: 0 4rpx 12rpx rgba(255, 107, 129, 0.4);
	transform: scale(1);
	animation: pulse 2s ease-in-out infinite;
	z-index: 10;
}

@keyframes pulse {
	0%, 100% {
		transform: scale(0.9);
	}
	50% {
		transform: scale(1);
	}
}

/* 内容区域 */
.content-wrapper {
	padding: 0 30rpx 40rpx;
	position: relative;
	z-index: 1;
}

.likes-section {
	min-height: 400rpx;
}

/* 点赞列表 */
.likes-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.like-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-radius: 24rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
	cursor: pointer;
	position: relative;
	overflow: hidden;
	-webkit-tap-highlight-color: transparent;
	tap-highlight-color: transparent;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.user-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.avatar-wrapper {
	position: relative;
	margin-right: 20rpx;
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 3rpx solid rgba(255, 255, 255, 0.8);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}



.user-details {
	flex: 1;
	min-width: 0;
}

.user-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.action-text {
	font-size: 24rpx;
	color: #666;
	opacity: 0.8;
}

.time-info {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 8rpx;
}

.like-time {
	font-size: 22rpx;
	color: #999;
	white-space: nowrap;
}

.heart-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
}

.heart-icon {
	width: 28rpx;
	height: 28rpx;
	position: relative;
}

.heart-icon::before {
	content: '';
	position: absolute;
	width: 28rpx;
	height: 28rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23ff6b81' stroke='%23ff6b81' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
	background-size: cover;
	background-repeat: no-repeat;
	animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
	0%, 100% {
		transform: scale(1);
		opacity: 0.8;
	}
	50% {
		transform: scale(1.1);
		opacity: 1;
	}
}

.message-preview {
	background: linear-gradient(135deg, rgba(255, 107, 129, 0.05), rgba(165, 199, 254, 0.05));
	padding: 20rpx;
	border-radius: 16rpx;
	border-left: 4rpx solid #ff6b81;
	position: relative;
}

.message-text {
	font-size: 26rpx;
	color: #555;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.empty-illustration {
	position: relative;
	margin-bottom: 40rpx;
	width: 160rpx;
	height: 160rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.empty-heart-icon {
	width: 80rpx;
	height: 80rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ddd' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
	background-size: cover;
	background-repeat: no-repeat;
	opacity: 0.4;
	animation: emptyFloat 3s ease-in-out infinite;
}

@keyframes emptyFloat {
	0%, 100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-10rpx);
	}
}

.empty-sparkles {
	position: absolute;
	width: 100%;
	height: 100%;
}

.sparkle {
	position: absolute;
	width: 8rpx;
	height: 8rpx;
	background: linear-gradient(45deg, #ff6b81, #a5c7fe);
	border-radius: 50%;
	opacity: 0;
	animation: sparkle 2s ease-in-out infinite;
}

.sparkle-1 {
	top: 20%;
	left: 20%;
	animation-delay: 0s;
}

.sparkle-2 {
	top: 30%;
	right: 15%;
	animation-delay: 0.7s;
}

.sparkle-3 {
	bottom: 25%;
	left: 30%;
	animation-delay: 1.4s;
}

@keyframes sparkle {
	0%, 100% {
		opacity: 0;
		transform: scale(0);
	}
	50% {
		opacity: 1;
		transform: scale(1);
	}
}

.empty-title {
	font-size: 32rpx;
	color: #666;
	font-weight: 600;
	display: block;
	margin-bottom: 15rpx;
}

.empty-subtitle {
	font-size: 26rpx;
	color: #999;
	line-height: 1.5;
	opacity: 0.8;
}

/* 加载状态 */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(10rpx);
	-webkit-backdrop-filter: blur(10rpx);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid rgba(255, 107, 129, 0.2);
	border-top: 4rpx solid #ff6b81;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
}
</style>
