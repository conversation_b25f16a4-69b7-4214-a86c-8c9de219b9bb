<template>
	<view class="profile-container">
		<!-- 顶部设计区域 -->
		<view class="profile-header">
			<!-- 更精美的装饰背景元素 -->
			<view class="decorative-circle circle-1"></view>
			<view class="decorative-circle circle-2"></view>
			<view class="decorative-shape shape-1"></view>
			<view class="decorative-shape shape-2"></view>
			<view class="decorative-pattern"></view>
			
			<!-- 优化磨砂玻璃效果卡片 -->
			<view class="glass-card user-info-card">
				<!-- 简化的头像容器结构 -->
				<view class="avatar-container">
					<!-- 微信平台按钮 -->
					<button v-if="$platform === 'WX'" class="avatar-button" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
						<image class="avatar-image" :src="formatAvatarUrl(userInfo.avatarUrl)" mode="aspectFill"></image>
						<!-- 编辑指示器 - 仅微信平台显示 -->
						<view class="avatar-edit-indicator">
							<view class="edit-icon"></view>
						</view>
					</button>
					<!-- QQ平台按钮 - 移除编辑指示器 -->
					<button v-else class="avatar-button" open-type="getUserInfo" @getuserinfo="onGetUserInfo">
						<image class="avatar-image" :src="formatAvatarUrl(userInfo.avatarUrl)" mode="aspectFill"></image>
					</button>
				</view>
				<view class="user-details">
					<!-- 非编辑状态显示昵称 -->
					<view v-if="!isEditingNickname" class="username-container" @tap="editNickname">
						<text class="username">{{userInfo.nickname || '匿名用户'}}</text>
						<view class="edit-icon-small"></view>
					</view>
					<!-- 编辑状态显示输入框 -->
					<view v-else class="nickname-input-container">
						<input 
							v-if="$platform === 'WX'" 
							type="nickname" 
							class="nickname-input" 
							:value="tempNickname" 
							@input="onNicknameInput"
							@blur="onNicknameBlur" 
							focus 
							placeholder="请输入昵称"
						/>
						<input 
							v-else 
							type="text" 
							class="nickname-input" 
							:value="tempNickname" 
							@input="onNicknameInput"
							@blur="onNicknameBlur" 
							focus 
							placeholder="请输入昵称"
						/>
					</view>
					<view class="user-badge">
						<view class="badge-dot"></view>
						<text class="badge-text">{{$platform === 'QQ' ? 'QQ' : '微信'}}用户</text>
						<!-- 添加拉黑状态显示 -->
						<view class="banned-badge" v-if="userInfo.is_banned">
							<text class="banned-text">已禁用</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 数据概览优化 -->
		<view class="stats-overview">
			<view class="stat-tile">
				<view class="stat-content">
					<text class="stat-count">{{userStats.sent || 0}}</text>
					<text class="stat-label">已发送</text>
				</view>
				<view class="stat-icon sent-icon">
					<view class="icon-pulse"></view>
				</view>
			</view>
			
			<!-- <view class="stat-tile" @tap="handleReceivedClick">
				<view class="stat-content">
					<text class="stat-count">0</text>
					<text class="stat-label">已收到</text>
				</view>
				<view class="stat-icon received-icon">
					<view class="icon-pulse"></view>
				</view>
			</view> -->

			<view class="stat-tile" @tap="handleLikesClick">
				<view class="stat-content">
					<text class="stat-count">{{userStats.likesCount || 0}}</text>
					<text class="stat-label">被点赞</text>
				</view>
				<view class="stat-icon likes-icon">
					<view class="icon-pulse"></view>
				</view>
			</view>

			<!-- <view class="stat-tile">
				<view class="stat-content">
					<text class="stat-count">{{userStats.views || 0}}</text>
					<text class="stat-label">浏览量</text>
				</view>
				<view class="stat-icon views-icon">
					<view class="icon-pulse"></view>
				</view>
			</view> -->
		</view>
		
		<!-- 优化主要内容区 -->
		<view class="main-content">
			<!-- 我的留言区域改进 -->
			<view class="section message-section">
				<view class="section-header">
					<view class="section-title-group">
						<view class="section-title-decoration"></view>
						<text class="section-title">我的留言</text>
					</view>
					<view class="view-all" @tap="viewAllMessages">
						<text class="view-all-text">查看全部</text>
						<view class="view-all-icon"></view>
					</view>
				</view>
				
				<!-- 改进留言列表UI -->
				<scroll-view 
					class="message-scroll" 
					scroll-x="true" 
					show-scrollbar="false"
					enhanced="true"
					:fast-deceleration="true"
					@scroll="handleMessageScroll"
				>
					<view class="message-list" v-if="myMessages.length > 0">
						<view 
							class="message-card" 
							v-for="(message, index) in myMessages" 
							:key="index"
							
						>
							<view class="message-card-inner">
								<view class="message-top">
									<view class="message-recipient">
										<text class="recipient-label">收信人</text>
										<text class="recipient-name">{{formatName(message.name)}}</text>
									</view>
									<view class="message-status" :class="{'sent': true}"></view>
								</view>
								<text class="message-content">{{message.content}}</text>
								<view class="message-meta">
									<text class="message-date">{{formatTime(message.created_at)}}</text>
									<view class="message-actions">
										<view class="message-action-icon reply-icon"></view>
									</view>
								</view>
							</view>
						</view>
						
						<!-- 修改显示条件，只要有留言就显示引导元素 -->
						<view class="view-more-card" @tap="viewAllMessages" v-if="myMessages.length > 0">
							<view class="view-more-inner">
								<view class="view-more-icon"></view>
								<text class="view-more-text">查看全部</text>
							</view>
						</view>
					</view>
					
					<view class="empty-messages" v-else>
						<view class="empty-illustration"></view>
						<text class="empty-text">还没有留言记录</text>
						<view 
							class="empty-action" 
							@tap="userInfo.is_banned ? showBannedTip() : goToWriteMessage()"
						>写第一封留言</view>
					</view>
				</scroll-view>
			</view>
			
			<!-- 功能区优化 -->
			<!-- <view class="section">
				<view class="section-header">
					<view class="section-title-group">
						<view class="section-title-decoration"></view>
						<text class="section-title">快捷功能</text>
					</view>
				</view>
				
				<view class="quick-actions">
					<view class="action-card" @tap="goToWriteMessage">
						<view class="action-icon compose-icon">
							<view class="action-icon-ripple"></view>
						</view>
						<text class="action-text">写留言</text>
					</view>
					
					<view class="action-card" @tap="refreshData">
						<view class="action-icon refresh-icon">
							<view class="action-icon-ripple"></view>
						</view>
						<text class="action-text">刷新</text>
					</view>
					
					<view class="action-card" @tap="showSettings">
						<view class="action-icon settings-icon">
							<view class="action-icon-ripple"></view>
						</view>
						<text class="action-text">设置</text>
					</view>
					
					<view class="action-card" @tap="shareProfile">
						<view class="action-icon share-icon">
							<view class="action-icon-ripple"></view>
						</view>
						<text class="action-text">分享</text>
					</view>
				</view>
			</view> -->
			
			<!-- 新增推荐功能区 -->
			<!-- <view class="section recommendation-section">
				<view class="recommendation-card" @tap="exploreMessages">
					<view class="recommendation-decoration"></view>
					<view class="recommendation-decoration-circle circle-1"></view>
					<view class="recommendation-decoration-circle circle-2"></view>
					
					<view class="recommendation-content">
						<view class="recommendation-icon-wrapper">
							<view class="recommendation-icon"></view>
							<view class="recommendation-icon-ring"></view>
						</view>
						<view class="recommendation-text">
							<text class="recommendation-title">探索热门留言</text>
							<text class="recommendation-desc">发现更多有趣的想法和故事</text>
						</view>
					</view>
					<view class="recommendation-action">
						<text class="recommendation-action-text">去探索</text>
						<view class="recommendation-action-arrow"></view>
					</view>
				</view>
			</view> -->

			<!-- 意见反馈 - 调整布局结构 -->
			<view class="section feedback-section">
				<view class="feedback-card" @tap="showFeedbackDialog">
					<!-- 添加背景装饰 -->
					<view class="feedback-decoration"></view>
					<view class="feedback-decoration-circle circle-1"></view>
					<view class="feedback-decoration-circle circle-2"></view>
					
					<view class="feedback-content">
						<view class="feedback-icon-wrapper">
							<view class="feedback-icon"></view>
							<view class="feedback-icon-ring"></view>
						</view>
						<view class="feedback-text-content">
							<text class="feedback-title">吐槽通道</text>
							<text class="feedback-desc">这里会因为你，而变得更好</text>
						</view>
						<!-- 移动到右侧 -->
						<view class="feedback-action">
							<text class="feedback-action-text">去吐槽</text>
							<view class="feedback-action-arrow"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 页脚 -->
		<view class="footer">
			<view class="footer-wave"></view>
			<text class="footer-slogan">所有的心意，都值得被珍藏</text>
			<view class="footer-decoration">
				<view class="footer-dot"></view>
				<view class="footer-line"></view>
				<view class="footer-dot"></view>
			</view>
		</view>
		
		<!-- 弹窗组件移到这里，放在页面根元素下 -->
		<!-- 反馈弹窗 -->
		<view class="feedback-dialog-mask" v-if="showDialog" @tap="closeDialog">
			<view class="feedback-dialog" @tap.stop>
				<view class="dialog-header">
					<text class="dialog-title">意见反馈</text>
					<view class="dialog-close" @tap="closeDialog"></view>
				</view>
				
				<view class="dialog-content">
					<view class="dialog-form">
						<view class="form-group">
							<text class="form-label">您的QQ/微信<text class="required">*</text></text>
							<input 
								class="form-input" 
								type="number" 
								placeholder="请输入您的QQ号/微信号"
								v-model="feedbackForm.qq"
								maxlength="11"
							/>
							<text class="form-error" v-if="formErrors.qq">{{ formErrors.qq }}</text>
						</view>
						
						<view class="form-group">
							<text class="form-label">反馈内容<text class="required">*</text></text>
							<textarea 
								class="form-textarea" 
								placeholder="请详细描述您的建议或者遇到的问题..."
								v-model="feedbackForm.content"
								maxlength="500"
							></textarea>
							<view class="textarea-counter">
								<text class="counter-text">{{ feedbackForm.content.length }}/500</text>
							</view>
							<text class="form-error" v-if="formErrors.content">{{ formErrors.content }}</text>
						</view>
						
						<!-- 暂时注释掉图片上传功能
						<view class="form-group screenshot-group">
							<text class="form-label">问题截图 <text class="label-hint">(可选)</text></text>
							<view class="screenshot-area">
								<view class="screenshot-item" v-for="(item, index) in feedbackForm.screenshots" :key="index">
									<image class="screenshot-image" :src="item" mode="aspectFill"></image>
									<view class="screenshot-delete" @tap="deleteScreenshot(index)">×</view>
								</view>
								
								<view class="screenshot-add" @tap="chooseImage" v-if="feedbackForm.screenshots.length < 3">
									<view class="add-icon"></view>
									<text class="add-text">添加图片</text>
								</view>
							</view>
						</view>
						-->
					</view>
				</view>
				
				<view class="dialog-footer">
					<button 
						class="submit-button" 
						:disabled="isSubmitting" 
						@tap="submitFeedback"
						:class="{'button-loading': isSubmitting}"
					>
						<text class="button-text" v-if="!isSubmitting">提交反馈</text>
						<view class="loading-dots" v-else>
							<view class="loading-dot"></view>
							<view class="loading-dot"></view>
							<view class="loading-dot"></view>
						</view>
					</button>
				</view>
			</view>
		</view>
		
		<!-- 提交成功弹窗 -->
		<view class="success-dialog-mask" v-if="showSuccessDialog" @tap="closeSuccessDialog">
			<view class="success-dialog" @tap.stop>
				<view class="success-icon"></view>
				<text class="success-title">提交成功</text>
				<text class="success-message">感谢您的反馈，我们会认真对待每一条建议！</text>
				<button class="success-button" @tap="closeSuccessDialog">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userInfo: {
					nickname: '',
					avatarUrl: '',
					is_banned: false,
					remark: ''
				},
				userStats: {
					sent: 0,
					received: 0,
					views: 0,
					likesCount: 0
				},
				myMessages: [], // 初始化为空数组
				token: null,
				isEditingNickname: false,
				tempNickname: '',
				isScrolledToEnd: false, // 添加滚动状态标记
				showDialog: false,
				showSuccessDialog: false,
				feedbackForm: {
					qq: '',
					content: '',
					screenshots: []
				},
				formErrors: {
					qq: '',
					content: ''
				},
				isSubmitting: false
			};
		},
		onLoad() {
			// 从App实例获取token和用户信息
			const app = getApp();
			this.token = app.globalData.token || uni.getStorageSync('mailbox_token');
			
			// 直接使用App.vue中已获取的用户信息
			if (app.globalData.userInfo) {
				this.userInfo = JSON.parse(JSON.stringify(app.globalData.userInfo));
				console.log('从App全局数据加载用户信息:', this.userInfo);
			}
		},
		onShow() {
			// 页面显示时加载数据，确保数据最新
			this.loadUserData();
			
			// 同步App中的最新用户信息
			const app = getApp();
			if (app.globalData.userInfo) {
				this.userInfo = JSON.parse(JSON.stringify(app.globalData.userInfo));
				console.log('onShow: 同步App全局用户信息');
			}
		},
		methods: {
			// --- 核心数据加载逻辑 ---
			async loadUserData() {
				try {
					// 确保获取到 Token
					await this.ensureToken();
					
					// 只获取统计数据和最近留言，用户信息由App.vue提供
					await Promise.all([
						this.fetchStats(),
						this.fetchMyRecentMessages()
					]);
					
				} catch (error) {
					console.error('加载用户数据失败:', error);
					uni.showToast({
						title: '加载数据失败',
						icon: 'none'
					});
				}
			},
			
			// --- 获取统计数据 ---
			async fetchStats() {
				if (!this.token) {
					console.error('无法获取统计数据：Token 不存在');
					return;
				}
				try {
					const res = await uni.request({
						url: `${this.$baseUrl}/mailbox/stats`,
						method: 'GET',
						header: {
							'Authorization': `Bearer ${this.token}`
						}
					});

					if (res.statusCode === 200 && res.data) {
						this.userStats.sent = res.data.sentCount;
						this.userStats.likesCount = res.data.likesCount;
						console.log('统计数据获取成功:', this.userStats);
					} else if (res.statusCode === 401) {
						console.warn('获取统计数据时Token失效，尝试重新登录...');
						const success = await this.handleTokenExpired();
						if (success) {
							await this.fetchStats();
						}
					} else {
						console.error('获取统计数据失败:', res);
						uni.showToast({
							title: '获取统计数据失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('请求统计数据接口失败:', error);
					uni.showToast({
						title: '网络请求失败',
						icon: 'none'
					});
				}
			},
			
			// --- 获取最近的我的留言 ---
			async fetchMyRecentMessages() {
				console.log('开始获取最近留言...');
				if (!this.token) {
					console.error('无法获取最近留言：Token 不存在');
					return;
				}
				try {
					const res = await uni.request({
						// 调用新的 API，并添加 limit 参数，修改为4条
						url: `${this.$baseUrl}/mailbox/my-messages?limit=4`, 
						method: 'GET',
						header: {
							'Authorization': `Bearer ${this.token}`
						}
					});

					if (res.statusCode === 200 && Array.isArray(res.data)) {
						this.myMessages = res.data;
						console.log('最近留言获取成功:', this.myMessages);
					} else if (res.statusCode === 401) {
						console.warn('获取最近留言时Token失效，尝试重新登录...');
						const success = await this.handleTokenExpired();
						if (success) {
							await this.fetchMyRecentMessages();
						}
					} else {
						console.error('获取最近留言失败:', res);
						// 即使失败，也清空或保持空数组，避免显示旧数据
						this.myMessages = []; 
						uni.showToast({
							title: '获取留言记录失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('请求最近留言接口失败:', error);
					this.myMessages = [];
					uni.showToast({
						title: '网络请求失败',
						icon: 'none'
					});
				}
			},
			
			// --- Token 相关方法 ---
			async ensureToken() {
				if (this.token) {
					return Promise.resolve();
				}
				
				// 尝试从App全局获取token
				const app = getApp();
				if (app.globalData.token) {
					this.token = app.globalData.token;
					return Promise.resolve();
				}
				
				// 如果App中也没有token，重定向到登录
				console.log('Token 不存在，等待App.vue完成登录');
				uni.showToast({
					title: '正在登录中，请稍后',
					icon: 'none'
				});
				return Promise.reject(new Error('等待登录'));
			},
			
			async handleTokenExpired() {
				console.log('Token 过期，请求App刷新');
				const app = getApp();
				
				// 更新本地token
				this.token = null;
				
				// 请求App执行登录流程
				try {
					await app.ensureLogin();
					// 登录成功后更新本地token和用户信息
					this.token = app.globalData.token;
					this.userInfo = JSON.parse(JSON.stringify(app.globalData.userInfo));
					return true;
				} catch (error) {
					console.error('刷新Token失败:', error);
					uni.showToast({
						title: '登录失效，请返回重试',
						icon: 'none'
					});
					return false;
				}
			},
			
			// --- 用户信息更新相关方法 ---
			onChooseAvatar(e) {
				// 微信平台选择头像的回调
				const avatarUrl = e.detail.avatarUrl;
				console.log('选择了新头像', avatarUrl);
				
				// 调用统一的更新方法
				this.updateProfile({ avatarTempPath: avatarUrl });
			},
			
			onGetUserInfo(e) {
				// 处理QQ平台getUserInfo按钮回调（头像或同步资料按钮）
				if (this.$platform !== 'QQ') return;
				
				if (e && e.detail && e.detail.userInfo) {
					// 获取成功，同步QQ资料
					console.log('获取QQ用户信息成功', e.detail.userInfo);
					const { nickName, avatarUrl } = e.detail.userInfo;
					
					// 显示处理中提示
					uni.showLoading({ title: '同步中...' });
					
					// 下载QQ头像文件并上传到服务器
					if (avatarUrl) {
						console.log('QQ头像URL:', avatarUrl);
						// 下载头像到本地临时文件
						uni.downloadFile({
							url: avatarUrl,
							success: res => {
								if (res.statusCode === 200) {
									console.log('QQ头像下载成功，临时文件路径:', res.tempFilePath);
									// 使用下载的临时文件路径上传
									this.updateProfile({
										avatarTempPath: res.tempFilePath,
										nickname: nickName
									});
								} else {
									console.error('QQ头像下载失败:', res);
									// 仅更新昵称
									this.updateProfile({ nickname: nickName });
									uni.showToast({
										title: '头像下载失败，仅更新昵称',
										icon: 'none'
									});
								}
							},
							fail: err => {
								console.error('QQ头像下载错误:', err);
								// 下载失败时仅更新昵称
								this.updateProfile({ nickname: nickName });
								uni.showToast({
									title: '头像下载失败，仅更新昵称',
									icon: 'none'
								});
							},
							complete: () => {
								uni.hideLoading();
							}
						});
					} else {
						// 无头像URL，只更新昵称
						this.updateProfile({ nickname: nickName });
						uni.hideLoading();
					}
				} else {
					// 用户拒绝授权或出错
					console.error('获取QQ用户信息失败', e);
					uni.showToast({
						title: '获取资料失败',
						icon: 'none'
					});
				}
			},
			
			editNickname() {
				// 开始编辑昵称
				this.isEditingNickname = true;
				this.tempNickname = this.userInfo.nickname || '';
			},
			
			onNicknameInput(e) {
				// 昵称输入时更新临时变量
				this.tempNickname = e.detail.value;
			},
			
			onNicknameBlur() {
				// 昵称输入框失去焦点时
				if (this.tempNickname && this.tempNickname !== this.userInfo.nickname) {
					// 昵称有变化且不为空，调用更新方法
					this.updateProfile({ nickname: this.tempNickname });
				}
				this.isEditingNickname = false;
			},
			
			async updateProfile(dataToUpdate) {
				// 统一处理资料更新的方法
				try {
					// 确保有token
					if (!this.token) {
						await this.ensureToken();
					}
					
					uni.showLoading({ title: '更新中' });
					
					// 判断更新类型并调用对应API
					if (dataToUpdate.avatarTempPath) {
						// 上传头像文件
						uni.uploadFile({
							url: `${this.$baseUrl}/mailbox/profile`,
							filePath: dataToUpdate.avatarTempPath,
							name: 'avatarFile',
							header: {
								'Authorization': `Bearer ${this.token}`
							},
							formData: dataToUpdate.nickname ? { nickname: dataToUpdate.nickname } : {},
							success: (uploadRes) => {
								try {
									// 添加响应状态码日志，帮助调试
									console.log('上传头像响应状态码:', uploadRes.statusCode);
									
									if (uploadRes.statusCode === 200) {
										const result = JSON.parse(uploadRes.data);
										// 更新成功
										if (result.avatarUrl) {
											this.userInfo.avatarUrl = result.avatarUrl;
										}
										if (result.nickname) { // 后端可能同时返回更新后的昵称
											this.userInfo.nickname = result.nickname;
										}
										
										// 同步更新App.vue中的用户信息
										const app = getApp();
										app.globalData.userInfo = JSON.parse(JSON.stringify(this.userInfo));
										app.refreshUserInfo(); // 触发App重新缓存
										
										console.log('头像更新成功，已同步至App全局数据');
										uni.showToast({ title: '头像更新成功' });
									} else {
										// 处理非200状态码
										let errorMsg;
										try {
											const result = JSON.parse(uploadRes.data);
											errorMsg = result.error || `服务器返回错误(${uploadRes.statusCode})`;
										} catch(e) {
											// 如果无法解析JSON（例如返回HTML），提供更友好的错误信息
											console.error('解析响应失败:', e, '原始响应:', uploadRes.data);
											errorMsg = `服务器返回了非JSON数据(${uploadRes.statusCode})`;
										}
										
										uni.showToast({
											title: errorMsg,
											icon: 'none'
										});
									}
								} catch (parseError) {
									console.error('处理上传响应时出错:', parseError, '原始响应:', uploadRes.data);
									uni.showToast({
										title: '解析服务器响应失败',
										icon: 'none'
									});
								}
							},
							fail: (err) => {
								console.error('上传头像请求失败', err);
								// 提供更详细的错误信息
								let errorMsg = '网络请求失败';
								if (err.errMsg) {
									if (err.errMsg.includes('fail timeout')) {
										errorMsg = '请求超时，请检查网络';
									} else if (err.errMsg.includes('fail')) {
										errorMsg = '请求失败，请稍后重试';
									}
								}
								uni.showToast({
									title: errorMsg,
									icon: 'none'
								});
							},
							complete: () => {
								uni.hideLoading();
							}
						});
					} else {
						// 只更新昵称或使用QQ头像URL
						const data = {};
						if (dataToUpdate.nickname) data.nickname = dataToUpdate.nickname;
						if (dataToUpdate.qqAvatarUrl) data.avatarUrl = dataToUpdate.qqAvatarUrl;
						
						console.log('发送资料更新请求:', data);
						
						const res = await uni.request({
							url: `${this.$baseUrl}/mailbox/profile`,
							method: 'POST',
							header: {
								'Authorization': `Bearer ${this.token}`,
								'Content-Type': 'application/json'
							},
							data
						});
						
						uni.hideLoading();
						
						console.log('资料更新响应:', res.statusCode, res.data);
						
						if (res.statusCode === 200) {
							// 更新成功
							if (res.data.nickname) this.userInfo.nickname = res.data.nickname;
							if (res.data.avatarUrl) this.userInfo.avatarUrl = res.data.avatarUrl;
							
							// 同步更新App.vue中的用户信息
							const app = getApp();
							app.globalData.userInfo = JSON.parse(JSON.stringify(this.userInfo));
							app.refreshUserInfo(); // 触发App重新缓存
							
							console.log('资料更新成功，已同步至App全局数据');
							uni.showToast({ title: '资料更新成功' });
						} else if (res.statusCode === 401) {
							// Token 失效
							console.warn('更新资料时Token失效，尝试重新登录...');
							const success = await this.handleTokenExpired();
							if (success) {
								await this.updateProfile(dataToUpdate); // 重试
							}
						} else if (res.statusCode === 405) {
							// Method Not Allowed - 特别处理这个常见错误
							console.error('服务器不支持POST方法，可能需要更新后端代码');
							uni.showToast({
								title: '服务器配置问题，请联系管理员',
								icon: 'none'
							});
						} else {
							// 其他错误
							uni.showToast({
								title: res.data?.error || `更新失败(${res.statusCode})`,
								icon: 'none'
							});
						}
					}
				} catch (error) {
					uni.hideLoading();
					console.error('更新资料异常', error);
					uni.showToast({
						title: '更新资料失败，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			},
			formatAvatarUrl(avatarUrl) {
				if (!avatarUrl) {
					return '/static/default-avatar.png';
				}
				// 如果是绝对URL，直接返回
				if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
					return avatarUrl;
				}
				// 如果是相对路径，但以/开头，添加baseUrl
				if (avatarUrl.startsWith('/')) {
					return this.$baseUrl + avatarUrl;
				}
				// 其他情况直接返回
				return avatarUrl;
			},
			// 添加滚动处理方法
			handleMessageScroll(e) {
				// 获取滚动位置
				const { scrollLeft, scrollWidth, deltaX } = e.detail;
				const scrollViewWidth = e.currentTarget.offsetWidth || 375; // 默认值
				
				// 计算是否滚动到最右侧（考虑到一些误差）
				this.isScrolledToEnd = scrollLeft + scrollViewWidth + 5 >= scrollWidth;
			},
			showFeedbackDialog() {
				this.showDialog = true;
			},
			closeDialog() {
				this.showDialog = false;
			},
			submitFeedback() {
				// 重置错误信息
				this.formErrors = {
					qq: '',
					content: ''
				};
				
				// 表单验证
				let isValid = true;
				
				// 验证QQ号
				if (!this.feedbackForm.qq) {
					this.formErrors.qq = 'QQ号/微信号不能为空';
					isValid = false;
				} else if (!/^[a-zA-Z0-9_-]{4,15}$/.test(this.feedbackForm.qq)) {
					this.formErrors.qq = 'QQ号/微信号格式不正确';
					isValid = false;
				}
				
				// 验证反馈内容
				if (!this.feedbackForm.content) {
					this.formErrors.content = '反馈内容不能为空';
					isValid = false;
				} else if (this.feedbackForm.content.length < 5) {
					this.formErrors.content = '反馈内容不能少于5个字符';
					isValid = false;
				}
				
				// 如果验证不通过，终止提交
				if (!isValid) return;
				
				// 开始提交
				this.isSubmitting = true;
				
				// 准备提交的数据
				const formData = {
					qq: this.feedbackForm.qq,
					content: this.feedbackForm.content,
					platform: this.$platform,
					timestamp: new Date().toISOString()
				};
				
				// 提交到后端API
				uni.request({
					url: `${this.$baseUrl}/mailbox/feedback`,
					method: 'POST',
					data: formData,
					header: {
						'Authorization': `Bearer ${this.token}`
					},
					success: (res) => {
						if (res.statusCode === 200) {
							// 提交成功
							setTimeout(() => {
								this.closeDialog();
								this.showSuccessDialog = true;
								// 重置表单
								this.feedbackForm = {
									qq: '',
									content: '',
									screenshots: []
								};
								this.formErrors = {
									qq: '',
									content: ''
								};
							}, 500);
						} else {
							// 提交失败
							uni.showToast({
								title: res.data.error || '提交失败，请稍后再试',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						console.error('提交反馈失败:', err);
						uni.showToast({
							title: '网络错误，请稍后再试',
							icon: 'none'
						});
					},
					complete: () => {
						setTimeout(() => {
							this.isSubmitting = false;
						}, 500);
					}
				});
			},
			deleteScreenshot(index) {
				this.feedbackForm.screenshots.splice(index, 1);
			},
			chooseImage() {
				uni.chooseImage({
					count: 3 - this.feedbackForm.screenshots.length, // 最多3张
					sizeType: ['compressed'], // 压缩图片
					sourceType: ['album', 'camera'], // 从相册或相机选择
					success: (res) => {
						// 合并已有图片和新选择的图片
						const newImages = res.tempFilePaths || [];
						this.feedbackForm.screenshots = [...this.feedbackForm.screenshots, ...newImages].slice(0, 3);
					},
					fail: (err) => {
						console.log('选择图片失败', err);
						// 失败时不需要特别处理
					}
				});
			},
			// 确保closeSuccessDialog方法正确实现
			closeSuccessDialog() {
				this.showSuccessDialog = false;
				this.showDialog = false; // 同时确保反馈弹窗也关闭
			},
			// 添加显示禁用提示的方法
			showBannedTip() {
				let tipMessage = '您的账号已被禁用';
				if (this.userInfo.remark) {
					tipMessage += `，原因：${this.userInfo.remark}`;
				}
				
				uni.showToast({
					title: tipMessage,
					icon: 'none',
					duration: 2000
				});
			},
			// 手动刷新用户信息的方法 - 委托给App.vue处理
			refreshUserInfo() {
				const app = getApp();
				app.refreshUserInfo().then(() => {
					// 刷新成功后，同步App中的用户信息到本页面
					this.userInfo = JSON.parse(JSON.stringify(app.globalData.userInfo));
					uni.showToast({title: '刷新成功'});
				});
			},
			
			// --- 其他页面方法 ---
			handleReceivedClick() {
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			},

			handleLikesClick() {
				uni.navigateTo({
					url: '/pages/message_likes/message_likes'
				});
			},
			
			// 查看全部消息 - 修改为跳转
			viewAllMessages() {
				uni.navigateTo({
					url: '/pages/my_messages/my_messages'
				});
			},
			
			viewMessageDetail(id) {
				uni.showToast({
					title: '查看详情开发中',
					icon: 'none'
				});
			},
			
			goToWriteMessage() {
				uni.navigateTo({
					url: '/pages/write/write'
				});
			},
			
			refreshData() {
				uni.showLoading({
					title: '刷新中'
				});
				this.loadUserData().finally(() => {
					uni.hideLoading();
				});
			},
			
			showSettings() {
				uni.showToast({
					title: '设置功能开发中',
					icon: 'none'
				});
			},
			
			shareProfile() {
				uni.showShareMenu({
					withShareTicket: true
				});
			},
			
			exploreMessages() {
				uni.showToast({
					title: '探索功能开发中',
					icon: 'none'
				});
			},
			
			formatName(name) {
				if (!name) return '匿名';
				if (name.length <= 1) return name;
				return name.charAt(0) + '**';
			},
			
			formatTime(timestamp) {
				if (!timestamp) return '';
				
				// 格式化时间字符串，将"-"替换为"/"以兼容iOS系统
				let formattedTimestamp = timestamp;
				if (typeof timestamp === 'string' && timestamp.includes('-')) {
					formattedTimestamp = timestamp.replace(/-/g, '/');
				}
				
				const date = new Date(formattedTimestamp);
				const now = new Date();
				const diff = now - date;
				const minutes = Math.floor(diff / 1000 / 60);
				const hours = Math.floor(minutes / 60);
				const days = Math.floor(hours / 24);
				
				if (minutes < 60) {
					return `${minutes}分钟前`;
				} else if (hours < 24) {
					return `${hours}小时前`;
				} else if (days < 30) {
					return `${days}天前`;
				} else {
					const MM = String(date.getMonth() + 1).padStart(2, '0');
					const dd = String(date.getDate()).padStart(2, '0');
					return `${MM}月${dd}日`;
				}
			}
		}
	}
</script>

<style>
/* 基础样式 */
.profile-container {
	min-height: 100vh;
	background: linear-gradient(180deg, #fdfbfb 0%, #f6f7fb 100%);
	padding: 0 0 40rpx;
	position: relative;
	overflow: hidden;
}

/* 顶部区域优化 */
.profile-header {
	height: 420rpx;
	position: relative;
	overflow: hidden;
	padding: 40rpx 40rpx 0;
}

/* 装饰元素优化 */
.decorative-circle {
	position: absolute;
	border-radius: 50%;
}

.circle-1 {
	width: 400rpx;
	height: 400rpx;
	background: linear-gradient(135deg, rgba(255, 207, 179, 0.7), rgba(255, 158, 158, 0.5));
	top: -200rpx;
	right: -100rpx;
	filter: blur(30rpx);
	animation: floatAnimation 8s ease-in-out infinite;
}

.circle-2 {
	width: 300rpx;
	height: 300rpx;
	background: linear-gradient(135deg, rgba(196, 224, 255, 0.7), rgba(168, 209, 242, 0.5));
	bottom: -150rpx;
	left: -80rpx;
	filter: blur(25rpx);
	animation: floatAnimation 10s ease-in-out infinite reverse;
}

@keyframes floatAnimation {
	0%, 100% {
		transform: translate(0, 0);
	}
	50% {
		transform: translate(15rpx, 15rpx);
	}
}

.decorative-shape {
	position: absolute;
	border-radius: 40rpx;
	transform: rotate(30deg);
}

.shape-1 {
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, rgba(255, 184, 222, 0.5), rgba(255, 121, 165, 0.3));
	top: 100rpx;
	left: 80rpx;
	filter: blur(10rpx);
	animation: rotateAnimation 15s linear infinite;
}

.shape-2 {
	width: 160rpx;
	height: 160rpx;
	background: linear-gradient(135deg, rgba(223, 200, 255, 0.6), rgba(192, 160, 236, 0.4));
	bottom: 40rpx;
	right: 100rpx;
	filter: blur(15rpx);
	animation: rotateAnimation 20s linear infinite reverse;
}

@keyframes rotateAnimation {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

/* 装饰图案 */
.decorative-pattern {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background-image: radial-gradient(rgba(255, 255, 255, 0.3) 1px, transparent 1px);
	background-size: 20rpx 20rpx;
	opacity: 0.5;
}

/* 磨砂玻璃卡片优化 */
.glass-card {
	background: rgba(255, 255, 255, 0.7);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-radius: 30rpx;
	box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.05);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
}

.user-info-card {
	padding: 40rpx;
	margin-top: 40rpx;
	display: flex;
	align-items: center;
	position: relative;
	z-index: 5;
}

/* 头像区域优化 */
.avatar-container {
	position: relative;
	width: 140rpx;
	height: 140rpx;
	margin-right: 30rpx;
}

/* 按钮样式重置与美化 */
.avatar-button {
	/* 重置按钮默认样式 */
	background: transparent;
	padding: 0;
	margin: 0;
	border: none;
	line-height: 1;
	
	/* 定位和尺寸 */
	position: relative;
	width: 120rpx;
	height: 120rpx;
	border-radius: 999rpx; /* 保证完美圆形 */
	
	/* 阴影效果增强立体感 */
	box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.08),
				0 3rpx 8rpx rgba(0, 0, 0, 0.12);
	
	/* 过渡效果 */
	transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
	overflow: visible;
}

/* 移除按钮默认边框 */
.avatar-button::after {
	border: none;
}

/* 按钮悬停/按下效果 */
.avatar-button:active {
	transform: scale(0.96);
	box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.06),
				0 2rpx 5rpx rgba(0, 0, 0, 0.08);
}

/* 头像图片样式 */
.avatar-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 999rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.9);
	
	/* 渐变边框效果 */
	box-shadow: 
		inset 0 0 0 1rpx rgba(255, 255, 255, 0.1),
		0 0 0 2rpx rgba(115, 103, 240, 0.15);
		
	/* 确保边框计算在内 */
	box-sizing: border-box;
	
	/* 图片裁剪模式 */
	object-fit: cover;
}

/* 编辑指示器样式 */
.avatar-edit-indicator {
	position: absolute;
	right: -5rpx;
	bottom: -5rpx;
	width: 40rpx;
	height: 40rpx;
	border-radius: 999rpx;
	background: #7367f0; /* 主题色 */
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 3rpx 8rpx rgba(115, 103, 240, 0.5);
	opacity: 0.9;
	transition: opacity 0.2s ease;
}

/* 编辑图标 */
.edit-icon {
	width: 20rpx;
	height: 20rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'%3E%3C/path%3E%3Cpath d='M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
}

/* 按钮悬停时编辑指示器的样式变化 */
.avatar-button:hover .avatar-edit-indicator {
	opacity: 1;
}

/* 用户信息优化 */
.user-details {
	flex: 1;
}

.username {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 10rpx;
	display: block;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.user-badge {
	display: inline-flex;
	align-items: center;
	background: rgba(79, 95, 232, 0.1);
	border-radius: 30rpx;
	padding: 6rpx 20rpx;
}

.badge-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: #4f5fe8;
	margin-right: 8rpx;
	animation: pulseDot 2s ease-in-out infinite;
}

@keyframes pulseDot {
	0%, 100% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.3);
		opacity: 0.7;
	}
}

.badge-text {
	font-size: 24rpx;
	color: #4f5fe8;
}

/* 数据概览优化 */
.stats-overview {
	display: flex;
	justify-content: space-around;
	padding: 0 30rpx;
	margin-top: -80rpx;
	position: relative;
	z-index: 10;
}

.stat-tile {
	width: 40%;
	background: #fff;
	border-radius: 24rpx;
	padding: 30rpx 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.03),
				0 5rpx 10rpx rgba(0, 0, 0, 0.02);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.stat-tile:active {
	transform: translateY(3rpx);
	box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.02);
}

.stat-content {
	display: flex;
	flex-direction: column;
	position: relative;
	z-index: 2;
}

.stat-count {
	font-size: 40rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 5rpx;
	position: relative;
}

.stat-count::after {
	content: '';
	position: absolute;
	width: 100%;
	height: 6rpx;
	border-radius: 3rpx;
	bottom: -4rpx;
	left: 0;
	background: linear-gradient(90deg, transparent, currentColor, transparent);
	opacity: 0.2;
}

.stat-label {
	font-size: 22rpx;
	color: #999;
}

.stat-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	z-index: 2;
}

.icon-pulse {
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: currentColor;
	opacity: 0.1;
	animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
	0%, 100% {
		transform: scale(1);
		opacity: 0.1;
	}
	50% {
		transform: scale(1.5);
		opacity: 0.05;
	}
}

.sent-icon {
	background: rgba(255, 111, 145, 0.1);
	color: #ff6f91;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff6f91' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 2L11 13'%3E%3C/path%3E%3Cpath d='M22 2L15 22L11 13L2 9L22 2Z'%3E%3C/path%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 30rpx;
}

.received-icon {
	background: rgba(118, 108, 219, 0.1);
	color: #766cdb;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23766cdb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 12h-6l-2 3h-4l-2-3H2'%3E%3C/path%3E%3Cpath d='M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z'%3E%3C/path%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 30rpx;
}

.likes-icon {
	background: rgba(118, 108, 219, 0.1);
	color: #766cdb;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23766cdb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 30rpx;
}

.views-icon {
	background: rgba(85, 188, 201, 0.1);
	color: #55bcc9;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2355bcc9' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z'%3E%3C/path%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 30rpx;
}

/* 主要内容区优化 */
.main-content {
	padding: 40rpx 30rpx 0;
}

/* 区块样式优化 */
.section {
	margin-bottom: 40rpx;
	position: relative;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title-group {
	display: flex;
	align-items: center;
}

.section-title-decoration {
	width: 8rpx;
	height: 32rpx;
	background: linear-gradient(180deg, #4f5fe8, #8a7fff);
	border-radius: 4rpx;
	margin-right: 16rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.view-all {
	display: flex;
	align-items: center;
	padding: 6rpx 20rpx;
	background: rgba(0, 0, 0, 0.03);
	border-radius: 30rpx;
	transition: all 0.3s ease;
}

.view-all:active {
	transform: scale(0.96);
	background: rgba(0, 0, 0, 0.05);
}

.view-all-text {
	font-size: 24rpx;
	color: #666;
}

.view-all-icon {
	width: 24rpx;
	height: 24rpx;
	margin-left: 6rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 16rpx;
}

/* 留言区域优化 */
.message-section {
	position: relative;
}

.message-section::after {
	content: '';
	position: absolute;
	bottom: -20rpx;
	left: 0;
	right: 0;
	height: 1rpx;
	background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
}

/* 留言列表优化 */
.message-scroll {
	width: 100%;
	white-space: nowrap;
	position: relative;
	overflow: hidden; /* 确保内容不会溢出 */
}

.message-list {
	display: inline-flex;
	padding: 10rpx 0 30rpx;
	flex-wrap: nowrap; /* 确保不换行 */
}

.message-card {
	width: 400rpx; /* 减小宽度，防止重叠 */
	height: 250rpx;
	margin-right: 30rpx; /* 增加右侧间距 */
	display: inline-block;
	position: relative;
	perspective: 1000rpx;
	flex-shrink: 0; /* 防止卡片被压缩 */
}

.message-card:last-child {
	margin-right: 40rpx; /* 最后一个卡片的右边距更大，确保全部显示 */
}

/* 留言卡片内部样式 */
.message-card-inner {
	width: 100%;
	height: 100%;
	padding: 25rpx; /* 略微减小内边距 */
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
	display: flex;
	flex-direction: column;
	transition: all 0.3s ease;
	transform-style: preserve-3d;
	position: relative;
	overflow: hidden;
	box-sizing: border-box; /* 确保内边距包含在总宽度内 */
}

/* 移除彩色条 */
/* .message-card-inner::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 6rpx;
	background: linear-gradient(90deg, #ff6f91, #4f5fe8);
	opacity: 0.8;
} */

.message-card:active .message-card-inner {
	transform: rotateY(5deg) scale(0.98);
	box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.03);
}

.message-top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.message-recipient {
	display: flex;
	align-items: center;
	max-width: 70%; /* 限制宽度，防止名称过长 */
	overflow: hidden;
}

.recipient-label {
	font-size: 22rpx;
	color: #999;
	margin-right: 10rpx;
}

.recipient-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #4f5fe8;
	position: relative;
}

.recipient-name::after {
	content: '';
	position: absolute;
	left: 0;
	bottom: -4rpx;
	width: 100%;
	height: 2rpx;
	background: currentColor;
	opacity: 0.3;
	transform: scaleX(0.7);
	transform-origin: left;
	transition: transform 0.3s ease;
}

.message-card:active .recipient-name::after {
	transform: scaleX(1);
}

.message-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	white-space: normal;
	flex: 1;
	word-break: break-all; /* 允许在任何字符间换行 */
}

.message-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 20rpx;
}

.message-date {
	font-size: 22rpx;
	color: #999;
}

.message-actions {
	display: flex;
	align-items: center;
}

.message-action-icon {
	width: 36rpx;
	height: 36rpx;
	opacity: 0.7;
	transition: all 0.3s ease;
}

.message-action-icon:active {
	opacity: 1;
	transform: scale(1.1);
}

.reply-icon {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%234f5fe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='15 10 20 15 15 20'%3E%3C/polyline%3E%3Cpath d='M4 4v7a4 4 0 0 0 4 4h12'%3E%3C/path%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 20rpx;
}

.message-status {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
}

.message-status.sent {
	background: #4f5fe8;
	box-shadow: 0 0 10rpx rgba(79, 95, 232, 0.5);
	position: relative;
}

.message-status.sent::after {
	content: '';
	position: absolute;
	top: -4rpx;
	left: -4rpx;
	right: -4rpx;
	bottom: -4rpx;
	border-radius: 50%;
	border: 1rpx solid currentColor;
	animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
	0%, 100% {
		transform: scale(1);
		opacity: 0.5;
	}
	50% {
		transform: scale(1.5);
		opacity: 0.1;
	}
}

/* 空状态优化 */
.empty-messages {
	width: 100%;
	height: 250rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.5);
	border-radius: 24rpx;
	border: 1rpx dashed rgba(0, 0, 0, 0.1);
}

.empty-illustration {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 20rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cccccc' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	opacity: 0.6;
}

.empty-text {
	font-size: 26rpx;
	color: #999;
	margin-bottom: 20rpx;
}

.empty-action {
	font-size: 24rpx;
	color: #4f5fe8;
	padding: 6rpx 24rpx;
	background: rgba(79, 95, 232, 0.1);
	border-radius: 20rpx;
}

/* 快捷操作区优化 */
.quick-actions {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.action-card {
	width: 22%;
	height: 160rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.03);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.action-card:active {
	transform: translateY(5rpx);
	box-shadow: 0 5rpx 10rpx rgba(0, 0, 0, 0.02);
}

.action-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 15rpx;
	position: relative;
}

.action-icon-ripple {
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 20rpx;
	background: currentColor;
	opacity: 0.05;
	animation: actionRipple 2s ease-in-out infinite;
}

@keyframes actionRipple {
	0%, 100% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.2);
	}
}

.compose-icon {
	background: linear-gradient(135deg, rgba(255, 111, 145, 0.1), rgba(255, 141, 168, 0.1));
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff6f91' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'%3E%3C/path%3E%3Cpath d='M18.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 36rpx;
	color: #ff6f91;
}

.refresh-icon {
	background: linear-gradient(135deg, rgba(118, 108, 219, 0.1), rgba(142, 134, 227, 0.1));
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23766cdb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='23 4 23 10 17 10'%3E%3C/polyline%3E%3Cpolyline points='1 20 1 14 7 14'%3E%3C/polyline%3E%3Cpath d='M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15'%3E%3C/path%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 36rpx;
	color: #766cdb;
}

.settings-icon {
	background: linear-gradient(135deg, rgba(85, 188, 201, 0.1), rgba(124, 207, 217, 0.1));
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2355bcc9' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3Cpath d='M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z'%3E%3C/path%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 36rpx;
	color: #55bcc9;
}

.share-icon {
	background: linear-gradient(135deg, rgba(254, 172, 94, 0.1), rgba(255, 189, 128, 0.1));
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23feac5e' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='18' cy='5' r='3'%3E%3C/circle%3E%3Ccircle cx='6' cy='12' r='3'%3E%3C/circle%3E%3Ccircle cx='18' cy='19' r='3'%3E%3C/circle%3E%3Cline x1='8.59' y1='13.51' x2='15.42' y2='17.49'%3E%3C/line%3E%3Cline x1='15.41' y1='6.51' x2='8.59' y2='10.49'%3E%3C/line%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 36rpx;
	color: #feac5e;
}

.action-text {
	font-size: 24rpx;
	color: #666;
}

/* 推荐功能区优化 */
.recommendation-section {
	margin-bottom: 30rpx;
}

.recommendation-card {
	position: relative;
	background: linear-gradient(135deg, #f9f9fc, #eef1f8);
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.05);
	display: flex;
	flex-direction: column;
	overflow: hidden;
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	transition: all 0.3s ease;
}

.recommendation-card:active {
	transform: translateY(2rpx) scale(0.99);
	box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.03);
}

/* 背景装饰元素 */
.recommendation-decoration {
	position: absolute;
	top: 0;
	right: 0;
	width: 200rpx;
	height: 200rpx;
	background: linear-gradient(135deg, rgba(143, 207, 255, 0.1), rgba(79, 95, 232, 0.1));
	transform: translate(50%, -50%) rotate(45deg);
	border-radius: 24rpx;
	z-index: 0;
}

.recommendation-decoration-circle {
	position: absolute;
	border-radius: 50%;
	background: linear-gradient(135deg, rgba(255, 111, 145, 0.1), rgba(255, 141, 168, 0.1));
	z-index: 0;
}

.recommendation-decoration-circle.circle-1 {
	width: 120rpx;
	height: 120rpx;
	bottom: -40rpx;
	right: 100rpx;
	opacity: 0.4;
}

.recommendation-decoration-circle.circle-2 {
	width: 80rpx;
	height: 80rpx;
	bottom: 30rpx;
	right: 30rpx;
	opacity: 0.6;
}

.recommendation-content {
	display: flex;
	align-items: center;
	position: relative;
	z-index: 1;
	margin-bottom: 30rpx;
}

.recommendation-icon-wrapper {
	position: relative;
	width: 80rpx;
	height: 80rpx;
	margin-right: 20rpx;
}

.recommendation-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
	background: linear-gradient(135deg, #766cdb, #4f5fe8);
	box-shadow: 0 8rpx 16rpx rgba(79, 95, 232, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	z-index: 2;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'%3E%3C/path%3E%3Cpolyline points='9 22 9 12 15 12 15 22'%3E%3C/polyline%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 40rpx;
}

.recommendation-icon-ring {
	position: absolute;
	top: -5rpx;
	left: -5rpx;
	right: -5rpx;
	bottom: -5rpx;
	border-radius: 25rpx;
	border: 2rpx dashed rgba(79, 95, 232, 0.3);
	animation: rotateRing 15s linear infinite;
	z-index: 1;
}

@keyframes rotateRing {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.recommendation-text {
	flex: 1;
}

.recommendation-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
	position: relative;
}

.recommendation-title::after {
	content: '';
	position: absolute;
	bottom: -6rpx;
	left: 0;
	width: 40rpx;
	height: 3rpx;
	background: linear-gradient(to right, #766cdb, transparent);
	border-radius: 3rpx;
}

.recommendation-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.recommendation-action {
	display: flex;
	align-items: center;
	background: linear-gradient(135deg, #766cdb, #4f5fe8);
	border-radius: 40rpx;
	padding: 16rpx 30rpx;
	align-self: flex-end;
	box-shadow: 0 8rpx 15rpx rgba(79, 95, 232, 0.15);
	transition: all 0.3s ease;
}

.recommendation-action:active {
	transform: scale(0.97);
	box-shadow: 0 4rpx 8rpx rgba(79, 95, 232, 0.1);
}

.recommendation-action-text {
	color: #fff;
	font-size: 26rpx;
	font-weight: 600;
	margin-right: 8rpx;
}

.recommendation-action-arrow {
	width: 24rpx;
	height: 24rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M5 12h14'%3E%3C/path%3E%3Cpath d='M12 5l7 7-7 7'%3E%3C/path%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: contain;
	animation: arrowMove 1.5s ease-in-out infinite;
}

@keyframes arrowMove {
	0%, 100% {
		transform: translateX(0);
	}
	50% {
		transform: translateX(4rpx);
	}
}

/* 页脚样式优化 */
.footer {
	padding: 40rpx 0;
	text-align: center;
	position: relative;
	margin-top: 60rpx;
}

.footer-wave {
	position: absolute;
	top: -30rpx;
	left: 0;
	right: 0;
	height: 60rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' fill='rgba(255, 255, 255, 0.7)'%3E%3C/path%3E%3C/svg%3E");
	background-size: cover;
	background-position: center;
}

.footer-slogan {
	font-size: 26rpx;
	color: #999;
	font-style: italic;
	display: block;
	margin-bottom: 20rpx;
}

.footer-decoration {
	display: flex;
	align-items: center;
	justify-content: center;
}

.footer-line {
	width: 60rpx;
	height: 2rpx;
	background: rgba(0, 0, 0, 0.1);
	margin: 0 10rpx;
}

.footer-dot {
	width: 6rpx;
	height: 6rpx;
	border-radius: 50%;
	background: rgba(0, 0, 0, 0.2);
}

/* 头像编辑按钮样式 */
.avatar-wrapper-button {
	/* 重置样式 */
	background: transparent;
	padding: 0;
	margin: 0;
	border: none;
	outline: none;
	line-height: normal; /* 重置行高 */
	overflow: visible; /* 避免可能的裁剪 */
	
	/* 设置尺寸和圆角 */
	width: 120rpx; /* 设置为和图片一样大 */
	height: 120rpx;
	border-radius: 50%;

	/* Flex 居中内部元素 */
	display: flex;
	align-items: center;
	justify-content: center;

	/* 相对定位，用于内部绝对定位元素（如光晕） */
	position: relative;
}
/* 移除uni-app按钮默认边框 */
.avatar-wrapper-button::after {
	border: none; 
}

.edit-icon {
	position: absolute;
	width: 36rpx;
	height: 36rpx;
	background: rgba(79, 95, 232, 0.9);
	border-radius: 50%;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
	border: 2rpx solid #fff;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 20h9'%3E%3C/path%3E%3Cpath d='M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
	background-position: center;
	background-repeat: no-repeat;
	background-size: 18rpx;
	z-index: 10;
}

.edit-icon-small {
	width: 28rpx;
	height: 28rpx;
	background: rgba(79, 95, 232, 0.1);
	border-radius: 50%;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	margin-left: 10rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%234f5fe8' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 20h9'%3E%3C/path%3E%3Cpath d='M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z'%3E%3C/path%3E%3C/svg%3E");
	background-position: center;
	background-repeat: no-repeat;
	background-size: 14rpx;
}

/* 用户名容器样式 */
.username-container {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

/* 昵称输入框容器 */
.nickname-input-container {
	margin-bottom: 10rpx;
}

.nickname-input {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	background: rgba(255, 255, 255, 0.5);
	border-bottom: 2rpx solid rgba(79, 95, 232, 0.3);
	padding: 4rpx 0;
	width: 100%;
}

/* 查看更多卡片样式 - 重新设计 */
.view-more-card {
	width: 180rpx;
	height: 250rpx;
	/* margin-right: 40rpx; */
	display: inline-flex;
	position: relative;
	align-items: center;
	justify-content: center;
}

.view-more-inner {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	position: relative;
}

.view-more-inner::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 3rpx;
	height: 60%;
	background: linear-gradient(to bottom, transparent, #4f5fe8, transparent);
	opacity: 0.5;
}

.view-more-inner:active {
	transform: scale(0.95);
}

.view-more-icon {
	width: 70rpx;
	height: 70rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 15rpx;
	position: relative;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%234f5fe8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='13 17 18 12 13 7'%3E%3C/polyline%3E%3Cpolyline points='6 17 11 12 6 7'%3E%3C/polyline%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 36rpx;
	animation: arrowPulse 1.5s ease-in-out infinite;
}

.view-more-text {
	font-size: 26rpx;
	color: #4f5fe8;
	font-weight: 500;
	text-align: center;
	position: relative;
}

.view-more-text::after {
	content: '';
	position: absolute;
	bottom: -6rpx;
	left: 0;
	width: 100%;
	height: 2rpx;
	background: currentColor;
	transform: scaleX(0.7);
	opacity: 0.5;
	transition: transform 0.3s ease;
}

.view-more-inner:active .view-more-text::after {
	transform: scaleX(1);
	opacity: 0.8;
}

/* 箭头动画效果 */
@keyframes arrowPulse {
	0%, 100% {
		transform: translateX(0);
		opacity: 0.9;
	}
	50% {
		transform: translateX(5rpx);
		opacity: 1;
	}
}

/* 意见反馈区样式优化 - 减小高度和调整布局 */
.feedback-section {
	margin-bottom: 30rpx;
}

.feedback-card {
	position: relative;
	background: linear-gradient(135deg, #f9f9fc, #eef1f8);
	border-radius: 24rpx;
	padding: 30rpx; /* 减小内边距 */
	box-shadow: 0 15rpx 30rpx rgba(0, 0, 0, 0.05);
	display: flex;
	overflow: hidden;
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	transition: all 0.3s ease;
}

.feedback-card:active {
	transform: translateY(2rpx) scale(0.99);
	box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.03);
}

/* 背景装饰元素 */
.feedback-decoration {
	position: absolute;
	top: 0;
	right: 0;
	width: 200rpx;
	height: 200rpx;
	background: linear-gradient(135deg, rgba(255, 111, 145, 0.1), rgba(255, 141, 168, 0.1));
	transform: translate(50%, -50%) rotate(45deg);
	border-radius: 24rpx;
	z-index: 0;
}

.feedback-decoration-circle {
	position: absolute;
	border-radius: 50%;
	background: linear-gradient(135deg, rgba(79, 95, 232, 0.1), rgba(118, 108, 219, 0.1));
	z-index: 0;
}

.feedback-decoration-circle.circle-1 {
	width: 120rpx;
	height: 120rpx;
	bottom: -40rpx;
	right: 100rpx;
	opacity: 0.4;
}

.feedback-decoration-circle.circle-2 {
	width: 80rpx;
	height: 80rpx;
	bottom: 30rpx;
	right: 30rpx;
	opacity: 0.6;
}

.feedback-content {
	display: flex;
	align-items: center;
	justify-content: space-between; /* 内容两端对齐 */
	position: relative;
	z-index: 1;
	width: 100%; /* 确保撑满整个容器 */
}

.feedback-icon-wrapper {
	position: relative;
	width: 70rpx; /* 稍微缩小尺寸 */
	height: 70rpx;
	margin-right: 20rpx;
}

.feedback-icon {
	width: 70rpx;
	height: 70rpx;
	border-radius: 18rpx; /* 调整圆角 */
	background: linear-gradient(135deg, #ff6f91, #ff8da8);
	box-shadow: 0 8rpx 16rpx rgba(255, 111, 145, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	z-index: 2;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff6f91' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z'%3E%3C/path%3E%3Cline x1='9' y1='9' x2='15' y2='9'%3E%3C/line%3E%3Cline x1='9' y1='13' x2='15' y2='13'%3E%3C/line%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 36rpx;
	background-color: #fff; /* 白色背景 */
}

.feedback-icon-ring {
	position: absolute;
	top: -5rpx;
	left: -5rpx;
	right: -5rpx;
	bottom: -5rpx;
	border-radius: 22rpx;
	border: 2rpx dashed rgba(255, 111, 145, 0.3);
	animation: rotateRing 15s linear infinite;
	z-index: 1;
}

.feedback-text-content {
	flex: 1;
}

.feedback-title {
	font-size: 30rpx; /* 稍微减小尺寸 */
	font-weight: 700;
	color: #333;
	margin-bottom: 6rpx; /* 减小间距 */
	display: block;
	position: relative;
}

.feedback-title::after {
	content: '';
	position: absolute;
	bottom: -4rpx; /* 减小间距 */
	left: 0;
	width: 40rpx;
	height: 3rpx;
	background: linear-gradient(to right, #ff6f91, transparent);
	border-radius: 3rpx;
}

.feedback-desc {
	font-size: 24rpx; /* 减小字体 */
	color: #666;
	display: block;
}

.feedback-action {
	display: flex;
	align-items: center;
	background: linear-gradient(135deg, #ff6f91, #ff8da8);
	border-radius: 30rpx; /* 减小圆角 */
	padding: 12rpx 24rpx; /* 减小内边距 */
	box-shadow: 0 8rpx 15rpx rgba(255, 111, 145, 0.15);
	transition: all 0.3s ease;
}

.feedback-action:active {
	transform: scale(0.97);
	box-shadow: 0 4rpx 8rpx rgba(255, 111, 145, 0.1);
}

.feedback-action-text {
	color: #fff;
	font-size: 24rpx; /* 减小字体 */
	font-weight: 600;
	margin-right: 6rpx; /* 减小间距 */
}

.feedback-action-arrow {
	width: 20rpx; /* 减小尺寸 */
	height: 20rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M5 12h14'%3E%3C/path%3E%3Cpath d='M12 5l7 7-7 7'%3E%3C/path%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: center;
	background-size: contain;
	animation: arrowMove 1.5s ease-in-out infinite;
}

@keyframes arrowMove {
	0%, 100% {
		transform: translateX(0);
	}
	50% {
		transform: translateX(4rpx);
	}
}

.dialog-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.dialog-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.dialog-close {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	background: rgba(0, 0, 0, 0.05);
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.dialog-close::before, .dialog-close::after {
	content: '';
	position: absolute;
	width: 18rpx;
	height: 2rpx;
	background: #666;
	transform-origin: center;
}

.dialog-close::before {
	transform: rotate(45deg);
}

.dialog-close::after {
	transform: rotate(-45deg);
}

.dialog-content {
	margin-bottom: 30rpx;
}

.dialog-form {
	display: flex;
	flex-direction: column;
}

.form-group {
	margin-bottom: 20rpx;
}

.form-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
	display: flex;
	align-items: center;
}

.required {
	color: #ff6f91;
	margin-left: 4rpx;
}

.label-hint {
	font-size: 22rpx;
	color: #999;
	font-weight: normal;
	margin-left: 10rpx;
}

.form-input {
	font-size: 28rpx;
	color: #333;
	background: #f7f8fc;
	border: 2rpx solid rgba(0, 0, 0, 0.05);
	border-radius: 12rpx;
	padding: 25rpx;
	box-sizing: border-box;
	width: 100%;
	transition: all 0.3s ease;
	height: 90rpx; /* 增加高度 */
}

.form-input:focus {
	border-color: rgba(79, 95, 232, 0.5);
	background: #fff;
}

.form-textarea {
	font-size: 28rpx;
	color: #333;
	background: #f7f8fc;
	border: 2rpx solid rgba(0, 0, 0, 0.05);
	border-radius: 12rpx;
	padding: 25rpx;
	box-sizing: border-box;
	width: 100%;
	height: 300rpx; /* 增加高度 */
	transition: all 0.3s ease;
	line-height: 1.6;
}

.form-textarea:focus {
	border-color: rgba(79, 95, 232, 0.5);
	background: #fff;
}

.textarea-counter {
	display: flex;
	justify-content: flex-end;
	font-size: 22rpx;
	color: #999;
	margin-top: 8rpx;
}

.screenshot-group {
	margin-bottom: 20rpx;
}

.screenshot-area {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	margin-top: 16rpx;
}

.screenshot-item {
	width: 140rpx;
	height: 140rpx;
	position: relative;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.screenshot-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.screenshot-delete {
	position: absolute;
	top: -6rpx;
	right: -6rpx;
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	background: rgba(255, 111, 145, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
	z-index: 2;
}

.screenshot-add {
	width: 140rpx;
	height: 140rpx;
	border-radius: 12rpx;
	background: #f7f8fc;
	border: 2rpx dashed rgba(0, 0, 0, 0.1);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.screenshot-add:active {
	background: #f0f2f7;
}

.add-icon {
	width: 50rpx;
	height: 50rpx;
	background: rgba(79, 95, 232, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	margin-bottom: 10rpx;
}

.add-icon::before, .add-icon::after {
	content: '';
	position: absolute;
	background: #4f5fe8;
}

.add-icon::before {
	width: 24rpx;
	height: 2rpx;
}

.add-icon::after {
	width: 2rpx;
	height: 24rpx;
}

.add-text {
	font-size: 22rpx;
	color: #666;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	margin-top: 20rpx;
}

.submit-button {
	padding: 0;
	height: 80rpx;
	width: 100%;
	background: linear-gradient(135deg, #4f5fe8, #766cdb);
	border-radius: 40rpx;
	color: #fff;
	font-size: 28rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	border: none;
	box-shadow: 0 8rpx 15rpx rgba(79, 95, 232, 0.2);
	transition: all 0.3s ease;
}

.submit-button:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 8rpx rgba(79, 95, 232, 0.15);
}

.submit-button[disabled] {
	background: #ccc;
	color: #fff;
	box-shadow: none;
}

.button-text {
	color: #fff;
	font-size: 28rpx;
	font-weight: 600;
}

.button-loading {
	background: linear-gradient(135deg, #595faa, #6662a3);
}

.loading-dots {
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: #fff;
	margin: 0 6rpx;
}

.loading-dot:nth-child(1) {
	animation: blink 1s ease-in-out infinite;
}

.loading-dot:nth-child(2) {
	animation: blink 1s ease-in-out 0.33s infinite;
}

.loading-dot:nth-child(3) {
	animation: blink 1s ease-in-out 0.66s infinite;
}

.success-dialog-mask, .feedback-dialog-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	box-sizing: border-box;
}

.feedback-dialog {
	background-color: #ffffff;
	border-radius: 24rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 90vh;
	overflow-y: auto;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
	padding: 40rpx;
	position: relative;
	box-sizing: border-box;
}

.form-error {
	font-size: 24rpx;
	color: #ff6f91;
	margin-top: 10rpx;
}

/* 成功弹窗样式 */
.success-dialog {
	width: 80%;
	max-width: 500rpx;
	background: #fff;
	border-radius: 24rpx;
	padding: 50rpx 40rpx;
	box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.15);
	display: flex;
	flex-direction: column;
	align-items: center;
	animation: popIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
	max-height: 90vh;
	overflow-y: auto;
}

@keyframes popIn {
	0% {
		transform: scale(0.8);
		opacity: 0;
	}
	80% {
		transform: scale(1.05);
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

.success-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	background: rgba(80, 230, 130, 0.15);
	margin-bottom: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.success-icon::before {
	content: '';
	width: 40rpx;
	height: 20rpx;
	border-left: 4rpx solid #50e682;
	border-bottom: 4rpx solid #50e682;
	transform: rotate(-45deg) translate(4rpx, -5rpx);
}

.success-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 20rpx;
}

.success-message {
	font-size: 28rpx;
	color: #666;
	text-align: center;
	line-height: 1.5;
	margin-bottom: 40rpx;
}

.success-button {
	width: 70%;
	height: 80rpx;
	background: linear-gradient(135deg, #4f5fe8, #766cdb);
	border-radius: 40rpx;
	color: #fff;
	font-size: 28rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	border: none;
	box-shadow: 0 8rpx 15rpx rgba(79, 95, 232, 0.2);
}

/* 禁用状态的徽章 */
.banned-badge {
	margin-left: 12rpx;
	background: rgba(255, 91, 91, 0.15);
	border-radius: 10rpx;
	padding: 4rpx 10rpx;
	display: flex;
	align-items: center;
}

.banned-text {
	font-size: 22rpx;
	color: #ff5b5b;
	font-weight: 500;
}

/* 空状态下禁用的按钮样式 */
.empty-action-disabled {
	background: rgba(153, 153, 153, 0.2);
	color: #999;
	cursor: not-allowed;
}
</style>