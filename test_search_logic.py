#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的留言检索逻辑
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:5000"  # 根据实际情况调整
TEST_TOKEN = "your_test_token_here"  # 需要替换为有效的JWT <PERSON>

def test_search_logic():
    """测试搜索逻辑的各种场景"""
    
    headers = {
        'Authorization': f'Bearer {TEST_TOKEN}',
        'Content-Type': 'application/json'
    }
    
    test_cases = [
        {
            'name': '单字符搜索 - 应该只返回收信人姓名为该字符的留言',
            'query': '张',
            'expected': '只返回收信人姓名确实是"张"的留言'
        },
        {
            'name': '多字符搜索 - 有结果',
            'query': '张三',
            'expected': '返回包含"张三"的留言'
        },
        {
            'name': '多字符搜索 - 无结果但有同姓推荐',
            'query': '张不存在的名字',
            'expected': '无主要结果，但提供姓"张"的推荐留言'
        }
    ]
    
    print("开始测试留言检索逻辑...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        print(f"搜索关键词: '{test_case['query']}'")
        print(f"预期结果: {test_case['expected']}")
        
        try:
            # 发送请求
            url = f"{BASE_URL}/mailbox/messages"
            params = {
                'query': test_case['query'],
                'page': 1,
                'pageSize': 10
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"响应状态: 成功")
                print(f"主要结果数量: {len(data.get('data', []))}")
                
                # 检查是否有推荐
                if 'recommendations' in data:
                    rec_data = data['recommendations']
                    print(f"推荐标题: {rec_data.get('title', '')}")
                    print(f"推荐数量: {len(rec_data.get('data', []))}")
                else:
                    print("无推荐内容")
                
                # 显示部分结果
                if data.get('data'):
                    print("主要结果示例:")
                    for msg in data['data'][:3]:  # 只显示前3条
                        print(f"  - 收信人: {msg.get('name')}, 内容: {msg.get('content', '')[:20]}...")
                
                if data.get('recommendations', {}).get('data'):
                    print("推荐结果示例:")
                    for msg in data['recommendations']['data'][:3]:  # 只显示前3条
                        print(f"  - 收信人: {msg.get('name')}, 内容: {msg.get('content', '')[:20]}...")
                        
            else:
                print(f"响应状态: 失败 ({response.status_code})")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")
        
        print("-" * 40)
    
    print("\n测试完成!")

def print_usage():
    """打印使用说明"""
    print("""
使用说明:
1. 确保后端服务正在运行 (python main.py)
2. 修改 BASE_URL 为实际的服务地址
3. 获取有效的JWT Token并替换 TEST_TOKEN
4. 运行此脚本: python test_search_logic.py

注意: 此脚本仅用于测试，需要有效的认证Token才能正常工作。
""")

if __name__ == "__main__":
    print_usage()
    
    # 检查是否配置了有效的Token
    if TEST_TOKEN == "your_test_token_here":
        print("⚠️  请先配置有效的JWT Token!")
        print("可以通过前端登录获取，或查看浏览器开发者工具中的请求头。")
    else:
        test_search_logic()
